"use client"

import { useState } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AuthStatus } from "@/components/auth/AuthStatus"
import { StartScreen } from "@/components/start-screen"
import { CaseSelectionScreen } from "@/components/case-selection-screen"
import { NewCaseScreen } from "@/components/new-case-screen"
import { WitnessSetupScreen } from "@/components/witness-setup-screen"
import { RecordingScreen } from "@/components/recording-screen"
import { TranscriptionScreen } from "@/components/transcription-screen"
import { StatementEditScreen } from "@/components/statement-edit-screen"
import { ExportScreen } from "@/components/export-screen"
import { CaseHistoryScreen } from "@/components/case-history-screen"
import { LoadingScreen } from "@/components/loading-screen"
import { CaseDetailsScreen } from "@/components/case-detail-screen"
import { InterviewDetailScreen } from "@/components/interview-detail-screen"
import type { <PERSON>, <PERSON>, Interview, User, Statement, TranscriptData } from "@/types/database"

export type Screen =
  | "start-screen"
  | "case-selection"
  | "new-case"
  | "witness-setup"
  | "recording-screen"
  | "transcription-screen"
  | "statement-edit"
  | "export-screen"
  | "case-history"
  | "case-details"
  | "interview-details"
  | "loading-screen"

export interface AppState {
  currentCase: Case | null
  currentWitness: Witness | null
  currentInterview: Interview | null
  selectedInterview: Interview | null
  isRecording: boolean
  isPaused: boolean
  recordingStartTime: number | null
  recordingEndTime: number | null
  transcriptData: TranscriptData | null
  summaryData: string | null,
  officerNotes: string | null,
  selectedCase: Case | null
  // Export data
  exportData: {
    interview: Interview | null
    case: Case | null
    officer: User | null
    transcriptData: TranscriptData | null
    statement: Statement | null
  } | null
}

export default function Home() {
  const [currentScreen, setCurrentScreen] = useState<Screen>("start-screen")
  const [appState, setAppState] = useState<AppState>({
    currentCase: null,
    currentWitness: null,
    currentInterview: null,
    selectedInterview: null,
    isRecording: false,
    isPaused: false,
    recordingStartTime: null,
    recordingEndTime: null,
    transcriptData: null,
    summaryData: null,
    officerNotes: null,
    selectedCase: null,
    exportData: null,
  })

  const navigateToScreen = (screen: Screen) => {
    setCurrentScreen(screen)
  }

  const updateAppState = (updates: Partial<AppState>) => {
    setAppState((prev) => ({ ...prev, ...updates }))
  }

  const resetAppState = () => {
    setAppState({
      currentCase: null,
      currentWitness: null,
      currentInterview: null,
      selectedInterview: null,
      isRecording: false,
      isPaused: false,
      recordingStartTime: null,
      recordingEndTime: null,
      transcriptData: null,
      summaryData: null,
      officerNotes: null,
      selectedCase: null,
      exportData: null,
    })
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case "start-screen":
        return <StartScreen onNavigate={navigateToScreen} />
      case "case-selection":
        return <CaseSelectionScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "new-case":
        return <NewCaseScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "witness-setup":
        return <WitnessSetupScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "recording-screen":
        return <RecordingScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "transcription-screen":
        return <TranscriptionScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "statement-edit":
        return <StatementEditScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "export-screen":
        return <ExportScreen onNavigate={navigateToScreen} appState={appState} resetAppState={resetAppState} />
      case "case-history":
        return <CaseHistoryScreen onNavigate={navigateToScreen} updateAppState={updateAppState}/>
      case "case-details":
        return <CaseDetailsScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState}/>
      case "interview-details":
        return <InterviewDetailScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState}/>
      case "loading-screen":
        return <LoadingScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      default:
        return <StartScreen onNavigate={navigateToScreen} />
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">{renderScreen()}</div>
    </ProtectedRoute>
)
}
