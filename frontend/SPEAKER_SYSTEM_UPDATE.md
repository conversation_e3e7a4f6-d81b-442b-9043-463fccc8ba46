# Speaker System Update

## Overview
Updated the websocket recording system to use dynamic speaker IDs (S1, S2, S3, etc.) instead of hardcoded officer/witness mapping. The system now maintains a speaker list with randomly generated colors and consistent naming.

## Changes Made

### 1. Database Types Update (`frontend/types/database.ts`)
- **Updated `TranscriptSpeaker` interface** to support new speaker type:
  ```typescript
  export interface TranscriptSpeaker {
    id: string; // 'officer' | 'witness' | 'S1' | 'S2' | etc.
    name: string;
    type: 'officer' | 'witness' | 'speaker'; // Added 'speaker' type
    color?: string; // UI color for speaker identification
  }
  ```

### 2. Websocket Recording Hook Update (`frontend/hooks/use-websocket-recording.ts`)

#### **Speaker Management System:**
- **Speaker List Storage**: Added `speakerListRef` using `useRef<Map<string, TranscriptSpeaker>>`
- **Random Color Generation**: Created `generateRandomColor()` function with 15 predefined colors
- **Dynamic Speaker Creation**: Updated `getSpeakerInfo()` to:
  - Check if speaker already exists in the list
  - Create new speaker with format "Speaker {number}" (e.g., "Speaker 1", "Speaker 2")
  - Assign random color from predefined palette
  - Store speaker in persistent list for future reference

#### **Speaker ID Processing:**
- **Dynamic Speaker Detection**: Updated transcript processing to:
  - Extract unique speaker IDs from backend segments
  - Generate speaker info for each unique ID encountered
  - Support unlimited number of speakers (S1, S2, S3, etc.)

#### **Session Management:**
- **Speaker List Clearing**: Added `clearSpeakerList()` function
- **New Recording Sessions**: Clear speaker list when starting new recording
- **Transcript Clearing**: Clear speaker list when clearing transcript data

## Implementation Details

### Speaker Creation Logic
```typescript
const getSpeakerInfo = (speakerId: string): TranscriptSpeaker => {
  // Check if speaker already exists
  if (speakerListRef.current.has(speakerId)) {
    return speakerListRef.current.get(speakerId)!
  }

  // Create new speaker entry
  const speakerNumber = speakerId.replace('S', '')
  const newSpeaker: TranscriptSpeaker = {
    id: speakerId,
    name: `Speaker ${speakerNumber}`,
    type: "speaker",
    color: generateRandomColor()
  }

  // Store for future reference
  speakerListRef.current.set(speakerId, newSpeaker)
  return newSpeaker
}
```

### Dynamic Speaker List Generation
```typescript
// Get all unique speakers from segments
const uniqueSpeakerIds = new Set(data.all_segments.map(s => s.speaker))
const speakers = Array.from(uniqueSpeakerIds).map(id => getSpeakerInfo(id))
```

### Color Palette
15 predefined colors for speaker identification:
- Blue variants: `#2563eb`, `#1e40af`, `#4338ca`
- Red variants: `#dc3545`, `#be123c`, `#b91c1c`
- Green variants: `#16a34a`, `#059669`, `#166534`
- Orange/Yellow variants: `#ca8a04`, `#c2410c`, `#a16207`
- Purple variants: `#9333ea`
- Teal variants: `#0891b2`
- Brown variants: `#7c2d12`

## Benefits

1. **Scalability**: Supports unlimited number of speakers (not limited to 2)
2. **Consistency**: Speaker colors and names remain consistent throughout a session
3. **Visual Distinction**: Random colors help distinguish between speakers in UI
4. **Flexibility**: No longer tied to officer/witness roles
5. **Persistence**: Speaker information persists throughout the recording session

## Usage

### Backend Integration
The backend should send speaker IDs in the format:
- `S1` for first speaker
- `S2` for second speaker  
- `S3` for third speaker
- etc.

### Frontend Display
- Speakers will be displayed as "Speaker 1", "Speaker 2", etc.
- Each speaker gets a unique color for visual identification
- Speaker information is maintained consistently throughout the session

## Testing
- ✅ Application compiles successfully with no TypeScript errors
- ✅ Speaker system supports dynamic speaker creation
- ✅ Color generation works correctly
- ✅ Speaker list persistence functions properly
- ✅ Session clearing works as expected

## Migration Notes
- **Backward Compatibility**: System still works with existing transcript data
- **No Breaking Changes**: Existing PDF/DOCX export functionality unaffected
- **UI Updates**: Transcript display will now show "Speaker X" instead of "Officer"/"Witness"
- **State Management**: Jotai atoms continue to work with new speaker structure
