"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useAtom } from 'jotai'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Pause, Play, Square, Wifi, WifiOff, AlertCircle, Database } from "lucide-react"
import { useWebSocketRecording } from "@/hooks/use-websocket-recording"
import {
  currentCaseAtom,
  currentWitnessAtom,
  isRecordingAtom,
  isPausedAtom,
  recordingStartTimeAtom,
  recordingEndTimeAtom,
  transcriptDataAtom,
  summaryDataAtom
} from "@/store/atoms"
import type { Screen } from "@/app/page"

interface RecordingScreenProps {
  onNavigate: (screen: Screen) => void
}

const WEBSOCKET_ENDPOINT = "ws://localhost:8081/ws/rt-audio"

export function RecordingScreen({ onNavigate }: RecordingScreenProps) {
  const [currentCase] = useAtom(currentCaseAtom)
  const [currentWitness] = useAtom(currentWitnessAtom)
  const [isRecording, setIsRecording] = useAtom(isRecordingAtom)
  const [isPaused, setIsPaused] = useAtom(isPausedAtom)
  const [recordingStartTime, setRecordingStartTime] = useAtom(recordingStartTimeAtom)
  const [recordingEndTime, setRecordingEndTime] = useAtom(recordingEndTimeAtom)
  const [, setTranscriptData] = useAtom(transcriptDataAtom)
  const [, setSummaryData] = useAtom(summaryDataAtom)

  const [recordingTime, setRecordingTime] = useState("00:00")
  const [startTime, setStartTime] = useState<number | null>(null)
  const [shouldNavigate, setShouldNavigate] = useState(false)
  const [lastUpdateTime, setLastUpdateTime] = useState(Date.now())

  const unmountedRef = useRef(false)
  const transcriptEndRef = useRef<HTMLDivElement>(null)

  const caseIdRef = useRef(currentCase?.id || `case_${Date.now()}`)
  const interviewIdRef = useRef(
    `interview_${Date.now()}_${currentWitness?.name?.replace(/\s+/g, "_") || "unknown"}`,
  )

  // --- DEBUG LOGGING: Only fires on mount/update ---
  const {
    isConnected,
    isRecording: wsIsRecording,
    isPaused: wsIsPaused,
    transcriptData,
    summary,
    databaseRecordId,
    error: wsError,
    startRecording: wsStartRecording,
    stopRecording: wsStopRecording,
    pauseRecording: wsPauseRecording,
    resumeRecording: wsResumeRecording,
  } = useWebSocketRecording({
    websocketUrl: WEBSOCKET_ENDPOINT,
    caseId: caseIdRef.current,
    interviewId: interviewIdRef.current,
    onTranscriptionUpdate: (data) => {
      setLastUpdateTime(Date.now())
      setTranscriptData(data) // Data should now be aggregated segments
    },
    onSummaryUpdate: (summaryText) => {
      setSummaryData(summaryText)
    },
    onDatabaseSaved: (recordId) => {
      console.log("Data saved to database with ID:", recordId)
    },
    onError: (error) => {
      console.error("WebSocket recording error:", error)
    },
    onConnectionChange: (connected) => {
      console.log("WebSocket connection status:", connected)
    },
    onFullyProcessed: () => {
      if (!unmountedRef.current) setShouldNavigate(true)
    },
  })

  // Auto-scroll to latest segment
  useEffect(() => {
    transcriptEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [transcriptData?.segments])

  // --- Navigate to transcription screen after proper cleanup ---
  useEffect(() => {
    if (shouldNavigate) {
      onNavigate("transcription-screen")
    }
    // eslint-disable-next-line
  }, [shouldNavigate])

  // --- Robust cleanup on unmount (stop all recording and ws) ---
  useEffect(() => {
    unmountedRef.current = false
    return () => {
      unmountedRef.current = true
      wsStopRecording()
      setIsRecording(false)
      setIsPaused(false)
    }
    // Only run on mount/unmount!
    // eslint-disable-next-line
  }, [])

  // --- Start recording on initial mount ---
  useEffect(() => {
    // Only start if *none* of the state indicates recording, and only once
    if (!isRecording && !wsIsRecording && !isConnected && !unmountedRef.current) {
      handleStartRecording()
    }
    // eslint-disable-next-line
  }, [])

  // --- Timer effect: Updates only while recording and not paused ---
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    if (isRecording && !isPaused && startTime) {
      interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        const minutes = Math.floor(elapsed / 60000)
        const seconds = Math.floor((elapsed % 60000) / 1000)
        setRecordingTime(`${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`)
      }, 1000)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isRecording, isPaused, startTime])

  // --- Safe wrapper to prevent duplicate start/stop ---
  const handleStartRecording = useCallback(async () => {
    if (isRecording || wsIsRecording) return
    try {
      const now = Date.now()
      setStartTime(now)
      await wsStartRecording()
      setIsRecording(true)
      setIsPaused(false)
      setRecordingStartTime(now)
    } catch (error) {
      console.error("Failed to start recording:", error)
    }
  }, [isRecording, wsIsRecording, wsStartRecording, setIsRecording, setIsPaused, setRecordingStartTime])

  const handlePauseRecording = useCallback(() => {
    if (!isRecording || wsIsPaused) return
    wsPauseRecording()
    setIsPaused(true)
  }, [isRecording, wsIsPaused, wsPauseRecording, setIsPaused])

  const handleResumeRecording = useCallback(() => {
    if (!isRecording || !wsIsPaused) return
    wsResumeRecording()
    setIsPaused(false)
  }, [isRecording, wsIsPaused, wsResumeRecording, setIsPaused])

  const handleStopRecording = useCallback(() => {
    if (!isRecording) return
    wsStopRecording()
    const endTime = Date.now()
    setIsRecording(false)
    setIsPaused(false)
    setRecordingEndTime(endTime)
    // DO NOT navigate immediately; wait for onFullyProcessed!
  }, [isRecording, wsStopRecording, setIsRecording, setIsPaused, setRecordingEndTime])

  const [storedTranscriptData] = useAtom(transcriptDataAtom)
  const currentTranscriptData = transcriptData || storedTranscriptData

  return (
    <div className="container mx-auto px-4 py-8 flex flex-col h-screen">
      <div className="flex items-center justify-between mb-8 pb-4 border-b flex-shrink-0">
        <h2 className="text-2xl font-semibold">Recording Interview</h2>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            {isConnected ? <Wifi className="w-4 h-4 text-green-500" /> : <WifiOff className="w-4 h-4 text-red-500" />}
            <span className="text-sm text-muted-foreground">{isConnected ? "Connected" : "Disconnected"}</span>
          </div>
          {databaseRecordId && (
            <div className="flex items-center gap-2">
              <Database className="w-4 h-4 text-blue-500" />
              <span className="text-sm text-muted-foreground">Saved</span>
            </div>
          )}
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <Badge variant="secondary">{recordingTime}</Badge>
          </div>
        </div>
      </div>

      {wsError && (
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{wsError}</AlertDescription>
        </Alert>
      )}

      <Card className="mb-8 flex-shrink-0">
        <CardContent className="p-4">
          <div className="space-y-2">
            <p>
              <strong>Case ID:</strong> {caseIdRef.current}
            </p>
            <p>
              <strong>Interview ID:</strong> {interviewIdRef.current}
            </p>
            <p>
              <strong>Witness:</strong> {currentWitness?.name || "Unknown"}
            </p>
            {databaseRecordId && (
              <p>
                <strong>Database Record:</strong> {databaseRecordId}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8 flex-1">
        <CardContent className="p-8">
          <div className="max-h-96 overflow-y-auto p-4">
            {currentTranscriptData?.segments && currentTranscriptData.segments.length > 0 ? (
              <>
                {currentTranscriptData.segments.map((segment, index) => {
                  const isStreaming = index === currentTranscriptData.segments.length - 1
                  const isRecent = Date.now() - lastUpdateTime < 2000 // 2 seconds
                  const speaker = currentTranscriptData.speakers.find((s) => s.id === segment.speaker)

                  return (
                    <div
                      key={index}
                      className={`flex gap-4 mb-6 ${isStreaming ? "opacity-75" : ""} ${isRecent && isStreaming ? "bg-blue-50 border-l-4 border-blue-400 pl-2" : ""}`}
                    >
                      <div
                        className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                        style={{ borderRightColor: speaker?.color }}
                      >
                        {speaker?.name}
                        {isStreaming && <span className="animate-pulse text-blue-500 ml-1">●</span>}
                        <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                      </div>
                      <div className="flex-1 text-sm leading-relaxed">
                        {segment.text}
                        {isStreaming && <span className="animate-pulse text-blue-500">|</span>}
                      </div>
                    </div>
                  )
                })}
                <div ref={transcriptEndRef} />
              </>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <p className="mb-2">Waiting for transcription...</p>
                <p className="text-sm">
                  {isConnected ? "Connected to transcription service" : "Connecting to transcription service..."}
                </p>
                {isConnected && wsIsRecording && <p className="text-xs mt-2">Speak now to see live transcription</p>}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {summary && (
        <Card className="mb-4 flex-shrink-0">
          <CardContent className="p-4">
            <h3 className="font-semibold mb-2">Interview Summary</h3>
            <p className="text-sm text-muted-foreground">{summary.substring(0, 200)}...</p>
          </CardContent>
        </Card>
      )}

      <div className="flex flex-col gap-4 max-w-sm mx-auto flex-shrink-0">
        {!wsIsPaused ? (
          <Button
            variant="secondary"
            size="lg"
            onClick={handlePauseRecording}
            className="flex items-center gap-2"
            disabled={!isRecording}
          >
            <Pause className="w-5 h-5" />
            Pause
          </Button>
        ) : (
          <Button
            variant="secondary"
            size="lg"
            onClick={handleResumeRecording}
            className="flex items-center gap-2"
            disabled={!isRecording}
          >
            <Play className="w-5 h-5" />
            Resume
          </Button>
        )}
        <Button
          size="lg"
          onClick={handleStopRecording}
          className="flex items-center gap-2"
          disabled={!isRecording}
        >
          <Square className="w-5 h-5" />
          Stop Interview
        </Button>
      </div>
    </div>
  )
}
