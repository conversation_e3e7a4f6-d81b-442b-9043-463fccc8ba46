"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { ArrowLeft, Loader2 } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { InterviewService, TranscriptionService, StatementService } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import type { Screen, AppState } from "@/app/page"

interface TranscriptionScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

export function TranscriptionScreen({ onNavigate, appState, updateAppState }: TranscriptionScreenProps) {
  const { user } = useAuth()
  const { toast } = useToast()
  const [isProcessing, setIsProcessing] = useState(false)

  const currentDate =
    new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }) +
    " at " +
    new Date().toLocaleTimeString("en-US")

  const handleApproveAndExport = async () => {
    if (!user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to approve and export interviews.",
        variant: "destructive",
      })
      return
    }

    if (!appState.currentCase) {
      toast({
        title: "Missing Case Information",
        description: "No case information found. Please start from the case selection.",
        variant: "destructive",
      })
      return
    }

    if (!appState.currentWitness) {
      toast({
        title: "Missing Witness Information",
        description: "No witness information found. Please complete witness setup.",
        variant: "destructive",
      })
      return
    }

    if (!appState.transcriptData || !appState.summaryData) {
      toast({
        title: "Missing Interview Data",
        description: "No transcription or summary data found. Please complete the recording.",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)

    try {
      // Calculate duration from actual recording times
      const startTime = appState.recordingStartTime
      const endTime = appState.recordingEndTime || Date.now()
      const durationSeconds = startTime ? Math.floor((endTime - startTime) / 1000) : 0

      // Step 1: Create the interview with actual timestamps
      const interviewData = {
        interviewing_officer_id: user.id,
        witness: {
          name: appState.currentWitness.name,
          type: appState.currentWitness.type,
          contact: appState.currentWitness.contact,
          environment: appState.currentWitness.environment,
        },
      }

      const interview = await InterviewService.createInterview(
        appState.currentCase.id,
        interviewData
      )

      // Step 2: Update interview with actual recording timestamps and duration
      const updatedInterview = await InterviewService.updateInterview(interview.id, {
        start_time: startTime ? new Date(startTime).toISOString() : undefined,
        end_time: new Date(endTime).toISOString(),
        duration_seconds: durationSeconds,
      })

      // Step 3: Create the transcription
      const officerSpeaker = appState.transcriptData.speakers.find(s => s.type === 'officer')
      const witnessSpeaker = appState.transcriptData.speakers.find(s => s.type === 'witness')

      await TranscriptionService.createTranscription(
        updatedInterview.id,
        appState.transcriptData,
        officerSpeaker?.name || user.fullName,
        witnessSpeaker?.name || appState.currentWitness.name
      )

      // Step 4: Create the statement with user-entered officer notes
      const officerNotes = appState.officerNotes || `Interview conducted on ${currentDate} by ${user.fullName}.`

      const createdStatement = await StatementService.createStatement(
        updatedInterview.id,
        appState.summaryData,
        officerNotes
      )

      toast({
        title: "Interview Saved Successfully",
        description: `Interview ID: ${updatedInterview.id}. The interview, transcription, and statement have been saved to the database.`,
      })

      // Prepare export data for the export screen
      updateAppState({
        currentInterview: updatedInterview,
        exportData: {
          interview: updatedInterview,
          case: appState.currentCase,
          officer: user,
          transcriptData: appState.transcriptData,
          statement: createdStatement,
        }
      })

      // Navigate to export screen
      onNavigate("export-screen")

    } catch (error) {
      console.error('Error saving interview:', error)
      toast({
        title: "Error Saving Interview",
        description: error instanceof Error ? error.message : "An unexpected error occurred while saving the interview.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <h2 className="text-2xl font-semibold">Statement Review</h2>
        <Button variant="outline" size="sm" onClick={() => onNavigate("recording-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Recording
        </Button>
      </div>

      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Interview Transcript</CardTitle>
          <p className="text-sm text-muted-foreground">Recorded: {currentDate}</p>
        </CardHeader>
        <CardContent className="p-0">
          <div className="max-h-96 overflow-y-auto p-4">
            {appState.transcriptData?.segments.map((segment, index) => {
              const speaker = appState.transcriptData?.speakers.find((s) => s.id === segment.speaker)
              return (
                <div key={index} className="flex gap-4 mb-4">
                  <div
                    className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                    style={{ borderRightColor: speaker?.color }}
                  >
                    {speaker?.name}
                    <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                  </div>
                  <div className="flex-1 text-sm leading-relaxed">{segment.text}</div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Auto-Generated Summary</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <p className="leading-relaxed">{appState.summaryData}</p>
        </CardContent>
      </Card>

      {/* Officer Notes Section */}
      <Card className="mb-8">
        <CardHeader className="bg-secondary">
          <CardTitle className="text-lg">Officer Notes</CardTitle>
          <p className="text-sm text-muted-foreground">Add any additional observations or notes about the interview</p>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-2">
            <Label htmlFor="officer-notes">Additional Notes</Label>
            <Textarea
              id="officer-notes"
              value={appState.officerNotes || ""}
              onChange={(e) => updateAppState({ officerNotes: e.target.value })}
              rows={4}
              placeholder="Enter any additional notes, observations, or important details about the interview..."
              className="min-h-[100px]"
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex gap-3">
        <Button
          variant="secondary"
          className="flex-1"
          onClick={() => onNavigate("statement-edit")}
          disabled={isProcessing}
        >
          Edit Statement
        </Button>
        <Button
          className="flex-1"
          onClick={handleApproveAndExport}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            "Approve & Export"
          )}
        </Button>
      </div>
    </div>
  )
}
