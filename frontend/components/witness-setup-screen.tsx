"use client"

import type React from "react"

import { useState } from "react"
import { useAtom } from 'jotai'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft } from "lucide-react"
import { currentWitnessAtom, currentCaseAtom } from "@/store/atoms"
import type { Screen } from "@/app/page"
import type { Witness, WitnessType, InterviewEnvironment } from "@/types/database"

interface WitnessSetupScreenProps {
  onNavigate: (screen: Screen) => void
}

export function WitnessSetupScreen({ onNavigate }: WitnessSetupScreenProps) {
  const [, setCurrentWitness] = useAtom(currentWitnessAtom)
  const [currentCase] = useAtom(currentCaseAtom)
  const [formData, setFormData] = useState({
    witnessName: "<PERSON>",
    witnessType: "<PERSON>ei<PERSON>bor",
    witnessContact: "555-0123",
    interviewEnvironment: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const witness: Witness = {
      name: formData.witnessName,
      type: formData.witnessType as WitnessType,
      contact: formData.witnessContact,
      environment: formData.interviewEnvironment as InterviewEnvironment,
    }

    setCurrentWitness(witness)
    onNavigate("recording-screen")
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("case-selection")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Witness Interview Setup</h2>
        <div></div>
      </div>

      {currentCase && (
        <Card className="mb-6">
          <CardContent className="p-4 text-center">
            <h4 className="font-semibold text-primary mb-2">{currentCase.id}</h4>
            <p className="text-muted-foreground">{currentCase.incidentLocation}</p>
          </CardContent>
        </Card>
      )}

      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="witness-name">Witness Name</Label>
              <Input
                id="witness-name"
                value={formData.witnessName}
                onChange={(e) => handleInputChange("witnessName", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="witness-type">Witness Type</Label>
              <Select value={formData.witnessType} onValueChange={(value) => handleInputChange("witnessType", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select type..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Resident">Resident</SelectItem>
                  <SelectItem value="Neighbor">Neighbor</SelectItem>
                  <SelectItem value="Passerby">Passerby</SelectItem>
                  <SelectItem value="Business Owner">Business Owner</SelectItem>
                  <SelectItem value="Emergency Responder">Emergency Responder</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="witness-contact">Contact Information</Label>
              <Input
                id="witness-contact"
                value={formData.witnessContact}
                onChange={(e) => handleInputChange("witnessContact", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="interview-environment">Interview Environment</Label>
              <Select
                value={formData.interviewEnvironment}
                onValueChange={(value) => handleInputChange("interviewEnvironment", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select environment..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="controlled">Controlled Environment (Station/Office)</SelectItem>
                  <SelectItem value="field">Field Environment (On-Scene)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button type="submit" className="w-full">
              Start Interview
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
